FROM oven/bun:1

ENV PRISMA_GENERATE_SKIP_DOWNLOAD=true
ENV PRISMA_GENERATE_SKIP_AUTOINSTALL=true
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# 1. Copy lock & package files for dependency install
COPY bun.lock* tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/

# 2. Create minimal package.json WITHOUT @prisma/client to avoid slow postinstall
RUN echo '{"name":"sumopod-server-build","workspaces":["./server","./shared"],"scripts":{"build:shared":"cd shared && bun run build","build:server":"cd server && bun run build"}}' > package.json

# 3. Temporarily remove @prisma/client from server package.json for faster install
RUN sed -i '/"@prisma\/client"/d' server/package.json

# 4. Install dependencies without Prisma (much faster)
RUN bun install

# 5. Now copy actual code after deps are cached
COPY shared ./shared
COPY server ./server

# 6. Build shared lib first
RUN cd shared && bun run build

# 7. Generate Prisma Client (now with schema available)
RUN cd server && bunx prisma generate

# 8. Install @prisma/client after generation (no slow postinstall)
RUN cd server && bun add @prisma/client --no-save

# 9. Build server
RUN cd server && bun run build

# 7. Setup entrypoint
COPY server/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

EXPOSE 8080
WORKDIR /app/server
ENTRYPOINT ["docker-entrypoint"]
CMD ["bun", "run", "start"]
