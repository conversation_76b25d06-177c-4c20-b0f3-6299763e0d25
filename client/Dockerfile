# Build stage
FROM oven/bun:1 AS builder

WORKDIR /app

# Copy root package.json and workspace files
COPY package.json bun.lock tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/
COPY client/package.json ./client/

# Install dependencies (skip postinstall scripts)
RUN bun install --ignore-scripts

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Skip server build for now - client doesn't need it for static build

# Copy client source code
COPY client ./client

# Build the client app
RUN cd client && bun run build

# Production stage
FROM nginx:alpine AS production

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy nginx config
COPY client/nginx.conf /etc/nginx/nginx.conf

# Create non-root user for nginx
RUN addgroup -g 1001 -S appuser && \
    adduser -S appuser -u 1001

# Set proper permissions
RUN chown -R appuser:appuser /usr/share/nginx/html && \
    chown -R appuser:appuser /var/cache/nginx && \
    chown -R appuser:appuser /var/log/nginx && \
    chown -R appuser:appuser /etc/nginx/conf.d

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
