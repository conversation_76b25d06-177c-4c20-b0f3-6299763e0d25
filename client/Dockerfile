# Build stage
FROM oven/bun:1.1.42 AS builder

WORKDIR /app

# Copy root package.json and workspace files
COPY package.json bun.lock tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/
COPY client/package.json ./client/

# Install dependencies (skip postinstall scripts)
RUN bun install --ignore-scripts

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Skip server build for now - client doesn't need it for static build

# Copy client source code
COPY client ./client

# Build the client app
RUN cd client && bun run build

# Production stage
FROM nginx:alpine AS production

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy nginx config
COPY client/nginx.conf /etc/nginx/nginx.conf

# Create directories and set permissions for nginx to run as non-root
RUN mkdir -p /var/cache/nginx /var/log/nginx /var/run /tmp /var/lib/nginx && \
    chown -R nginx:nginx /var/cache/nginx /var/log/nginx /var/run /usr/share/nginx/html /etc/nginx /tmp /var/lib/nginx && \
    chmod -R 755 /var/cache/nginx /var/log/nginx /var/run /tmp /var/lib/nginx && \
    touch /var/run/nginx.pid && \
    chown nginx:nginx /var/run/nginx.pid

# Switch to nginx user
USER nginx

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
